import { Routes } from '@angular/router';
import { PromptSubmissionGuard } from './shared/guards/prompt-submission.guard';
import { CardSelectionGuard } from './shared/guards/card-selection.guard';

export const routes: Routes = [
  // Main landing page route
  {
    path: '',
    loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
    data: { preload: true }
  },
  // Legacy route - keep for backward compatibility
  {
    path: 'prompt',
    loadComponent: () =>
      import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
        m => m.PromptContentComponent
      ),
  },
  // Legacy route - keep for backward compatibility
  {
    path: 'code-preview',
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    canActivate: [PromptSubmissionGuard],
  },
  // Generate Application route - handles both prompt and code-preview states
  {
    path: 'generate-application',
    loadComponent: () =>
      import('./shared/components/application-flow/application-flow.component').then(
        m => m.ApplicationFlowComponent
      ),
    canActivate: [CardSelectionGuard],
    data: { cardType: 'Generate Application' }
  },
  // Generate Application project loading route
  {
    path: 'generate-application/projects/:projectId',
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Generate Application',
      isProjectLoading: true,
      skipGuards: true
    }
  },
  // New route for project loading
  {
    path: 'project/:projectId',
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Project Loading',
      isProjectLoading: true,
      skipGuards: true
    }
  },
  // Generate UI Design route - handles both prompt and code-preview states
  {
    path: 'generate-ui-design',
    loadComponent: () =>
      import('./shared/components/ui-design-flow/ui-design-flow.component').then(
        m => m.UIDesignFlowComponent
      ),
    canActivate: [CardSelectionGuard],
    data: { cardType: 'Generate UI Design' }
  },
  // Generate UI Design project loading route
  {
    path: 'generate-ui-design/projects/:projectId',
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Generate UI Design',
      isProjectLoading: true,
      skipGuards: true
    }
  },
  // Catch-all route - redirect to root
  {
    path: '**',
    redirectTo: '',
  },
];
